// ModelTrain 组合式函数
import { computed, reactive, ref, watch, nextTick } from 'vue'
import { toast } from 'vue-sonner'
import { useDebounceFn } from '@vueuse/core'
import {
  getNodeParams,
  saveNodeParams,
  updateNodeData as updateNodeDataUtil,
} from '@renderer/utils/nodeUtils'
import { useFlowsStore, useTaskStore } from '@renderer/store'
import { createStudyService } from '@renderer/config/api/grpc/studyService'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { downloadModelFile } from '@renderer/utils/utils'

import {
  type DatasetData,
  type DatasetRatios,
  hasAnyAvailableData,
  convertApiDataToDataset,
  generateTrainingData,
} from '../utils/datasetUtils'
import {
  type ModelParams,
  type ModelResult,
  createDefaultModelParams,
  createDefaultModelResult,
  updateModelParamsFromConfig,
  updateModelResultFromConfig,
} from '../utils/paramUtils'

export interface ModelTrainState {
  isProcessing: boolean
  taskProgress: number
  modelResult: ModelResult
  datasetData: DatasetData
  datasetRatios: DatasetRatios
  trainParams: {
    batchSize: number
    epoch: number
  }
  checkInputBatteryData: {
    taskId: string
    inputDatas: string
    result: any
  }
  trainTask: {
    taskId: string
    result: any
  }
  exportTask: {
    taskId: string
    result: any
    isExporting: boolean
  }
}

export const useModelTrain = (props: { nodeData: any }) => {
  const taskStore = useTaskStore()
  const flowsStore = useFlowsStore()
  const service = createStudyService()
  const taskService = createTaskService()

  // 状态管理
  const state = reactive<ModelTrainState>({
    isProcessing: false,
    taskProgress: 0,
    modelResult: createDefaultModelResult(),
    datasetData: {
      allData: [],
      trainData: [],
      testData: [],
      valData: [],
      supportData: [],
    },
    datasetRatios: { train: 60, test: 20, val: 10, support: 10 },
    trainParams: {
      batchSize: 32,
      epoch: 8,
    },
    checkInputBatteryData: {
      taskId: '',
      inputDatas: '',
      result: null,
    },
    trainTask: {
      taskId: '',
      result: null,
    },
    exportTask: {
      taskId: '',
      result: null,
      isExporting: false,
    },
  })

  // 对话框状态
  const showDatasetRatioDialog = ref(false)
  const showCustomDatasetDialog = ref(false)
  const showSubmitDialog = ref(false)

  // 计算属性
  const trainModelConfig = computed(() => {
    if (props.nodeData?.id && props.nodeData?.data?.workflowId) {
      const params = flowsStore.getNodeParams(props.nodeData.data.workflowId, props.nodeData.id)
      return params?.TrainModelConfig || {}
    }
    return {}
  })
  console.log('trainModelConfig==============>', trainModelConfig.value)

  const trainInputData = computed(() => {
    if (props.nodeData?.id && props.nodeData?.data?.workflowId) {
      const params = flowsStore.getNodeParams(props.nodeData.data.workflowId, props.nodeData.id)
      return params?.TrainInputData?.input_datas || ''
    }
    return ''
  })

  const isLoadingBatteryData = computed(() => {
    // 如果有数据集数据，则不显示加载状态
    if (hasAnyAvailableData(state.datasetData, null)) {
      return false
    }

    // 检查任务状态
    const taskId = state.checkInputBatteryData.taskId
    if (!taskId) return false

    const taskStatus = taskStore.getTaskStatus(taskId).value
    return taskStatus && !['Finished', 'Error', 'Abort'].includes(taskStatus)
  })

  // 计算训练任务进度
  const trainTaskProgress = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return 0

    const progress = taskStore.getTaskProgress(taskId).value
    return progress || 0
  })

  // 计算训练任务是否正在进行
  const isTrainTaskProcessing = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return false

    const taskStatus = taskStore.getTaskStatus(taskId).value
    return ['Computing', 'Pending', 'Initializing'].includes(taskStatus)
  })

  // 计算训练任务状态
  const trainTaskStatus = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return 'Unknown'

    return taskStore.getTaskStatus(taskId).value
  })

  // 计算训练任务是否完成
  const trainTaskCompleted = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return false

    const taskStatus = taskStore.getTaskStatus(taskId).value
    const isTaskFinished = ['Finished', 'Error', 'Abort'].includes(taskStatus)
    const hasResult = !!state.trainTask.result

    return isTaskFinished && hasResult
  })

  // 计算训练任务持续时间
  const trainTaskDuration = computed(() => {
    const taskId = state.trainTask.taskId
    if (!taskId) return '--'

    const task = taskStore.tasks.find((t) => t.taskId === taskId)
    return task?.duration || '--'
  })

  // 计算是否可以显示导出按钮
  const canShowExportButton = computed(() => {
    // 必须有训练结果
    if (!state.trainTask.result) return false

    // 检查 epoch 是否大于 0
    const epoch = state.trainTask.result.epoch
    return epoch && epoch > 0
  })

  // 服务器信息
  const lastServerInfo = ref({ server_id: '', service_name: '' })

  // 统一的状态保存函数
  const saveCurrentState = useDebounceFn(() => {
    const paramsToSave = {
      modelResult: state.modelResult,
      modelParamsModified: true,
      datasetData: state.datasetData,
      datasetRatios: state.datasetRatios,
      trainParams: state.trainParams,
      checkInputBatteryData: {
        taskId: state.checkInputBatteryData.taskId,
        inputDatas: state.checkInputBatteryData.inputDatas,
        result: state.checkInputBatteryData.result, // 保存原始结果用于重新分配
      },
      trainTask: {
        taskId: state.trainTask.taskId,
        result: state.trainTask.result,
      },
      lastServerInfo: {
        server_id: lastServerInfo.value.server_id,
        service_name: lastServerInfo.value.service_name,
      },
    }

    saveNodeParams(props.nodeData, paramsToSave)
    console.log('已保存当前状态:', paramsToSave)
  }, 300)

  // 立即保存状态的函数
  const saveCurrentStateImmediate = () => {
    const paramsToSave = {
      modelResult: state.modelResult,
      modelParamsModified: true,
      datasetData: state.datasetData,
      datasetRatios: state.datasetRatios,
      trainParams: state.trainParams,
      checkInputBatteryData: {
        taskId: state.checkInputBatteryData.taskId,
        inputDatas: state.checkInputBatteryData.inputDatas,
        result: state.checkInputBatteryData.result,
      },
      trainTask: {
        taskId: state.trainTask.taskId,
        result: state.trainTask.result,
      },
      exportTask: {
        taskId: state.exportTask.taskId,
        result: state.exportTask.result,
        isExporting: state.exportTask.isExporting,
      },
      lastServerInfo: {
        server_id: lastServerInfo.value.server_id,
        service_name: lastServerInfo.value.service_name,
      },
    }

    saveNodeParams(props.nodeData, paramsToSave)
    console.log('已立即保存当前状态:', paramsToSave)
  }

  // 从存储中恢复状态
  const restoreStateFromStorage = () => {
    const savedParams = getNodeParams(props.nodeData)
    console.log('从存储中获取的参数:', savedParams)

    if (savedParams) {
      // 使用新的 modelResult 结构
      if (savedParams.modelResult) {
        Object.assign(state.modelResult, savedParams.modelResult)
      }

      if (savedParams.datasetData) {
        Object.assign(state.datasetData, savedParams.datasetData)
      }
      if (savedParams.datasetRatios) {
        Object.assign(state.datasetRatios, savedParams.datasetRatios)
      }
      if (savedParams.trainParams) {
        Object.assign(state.trainParams, savedParams.trainParams)
      }
      if (savedParams.checkInputBatteryData) {
        state.checkInputBatteryData.taskId = savedParams.checkInputBatteryData.taskId || ''
        state.checkInputBatteryData.inputDatas = savedParams.checkInputBatteryData.inputDatas || ''
        // 恢复原始结果数据
        if (savedParams.checkInputBatteryData.result) {
          state.checkInputBatteryData.result = savedParams.checkInputBatteryData.result
        }
      }
      if (savedParams.trainTask) {
        state.trainTask.taskId = savedParams.trainTask.taskId || ''
        if (savedParams.trainTask.result) {
          state.trainTask.result = savedParams.trainTask.result
        }
      }
      if (savedParams.exportTask) {
        state.exportTask.taskId = savedParams.exportTask.taskId || ''
        state.exportTask.isExporting = savedParams.exportTask.isExporting || false
        if (savedParams.exportTask.result) {
          state.exportTask.result = savedParams.exportTask.result
        }
      }
      // 恢复服务器信息
      if (savedParams.lastServerInfo) {
        lastServerInfo.value = {
          server_id: savedParams.lastServerInfo.server_id || '',
          service_name: savedParams.lastServerInfo.service_name || '',
        }
        // console.log('已恢复服务器信息:', lastServerInfo.value)
      }
      return true
    }
    return false
  }

  // 更新数据集数据
  const updateDatasetFromApiResult = (ratios?: DatasetRatios) => {
    if (state.checkInputBatteryData.result) {
      // 使用传入的比例，如果没有则使用当前存储的比例
      const useRatios = ratios || state.datasetRatios
      const convertedData = convertApiDataToDataset(state.checkInputBatteryData.result, useRatios)

      state.datasetData.trainData = convertedData.trainData
      state.datasetData.testData = convertedData.testData
      state.datasetData.valData = convertedData.valData
      state.datasetData.supportData = convertedData.supportData

      // 更新 allData，合并所有数据集
      state.datasetData.allData = [
        ...state.datasetData.trainData,
        ...state.datasetData.testData,
        ...state.datasetData.valData,
        ...state.datasetData.supportData,
      ]

      console.log('数据集已更新，使用比例:', useRatios, '数据集:', state.datasetData)
      saveCurrentState()
    } else {
      // 尝试使用存储的数据
      const savedParams = getNodeParams(props.nodeData)
      if (savedParams?.datasetData) {
        const hasStoredData = hasAnyAvailableData(savedParams.datasetData, null)
        if (hasStoredData) {
          Object.assign(state.datasetData, savedParams.datasetData)
          console.log('接口无新数据，使用存储的数据集数据:', state.datasetData)
        }
      }
    }
  }

  // 统一的 allData 缓存更新函数
  const updateAllDataCache = () => {
    state.datasetData.allData = [
      ...state.datasetData.trainData,
      ...state.datasetData.testData,
      ...state.datasetData.valData,
      ...state.datasetData.supportData,
    ]
    console.log('updateAllDataCache=================>>>', state.datasetData)
  }

  // 检查输入电池数据
  const checkInputBatteryData = async (inputs: any) => {
    const inputsRes = await service.checkInputBatteryData(inputs)
    if (Number(inputsRes.statusCode) === 200) {
      const taskId = inputsRes.taskId
      state.checkInputBatteryData.taskId = taskId
      taskStore.startPolling(taskId)
    }
  }

  // 数据验证函数
  const validateDataAvailability = (): boolean => {
    return hasAnyAvailableData(state.datasetData, state.checkInputBatteryData.result)
  }

  // 业务逻辑函数
  const handleUseRecommendedDataset = () => {
    console.log('打开数据集比例配置对话框')
    if (!validateDataAvailability()) {
      toast.error('暂无数据，请先等待数据加载完成')
      return
    }
    showDatasetRatioDialog.value = true
  }

  const handleOpenCustomDataset = () => {
    console.log('打开自定义数据集对话框')
    if (!validateDataAvailability()) {
      toast.error('暂无数据，请先等待数据加载完成')
      return
    }
    showCustomDatasetDialog.value = true
  }

  const handleDatasetRatioConfirm = (ratios: DatasetRatios) => {
    console.log('确认数据集比例配置:', ratios)

    // 先保存比例配置
    Object.assign(state.datasetRatios, ratios)

    // 更新数据集数据
    updateDatasetFromApiResult(ratios)

    // 批量设置推荐状态
    const allDataArrays = [
      state.datasetData.trainData,
      state.datasetData.testData,
      state.datasetData.valData,
      state.datasetData.supportData,
    ]

    allDataArrays.forEach((items) => {
      items.forEach((item) => {
        item.isRecommended = true
      })
    })

    // 一次性更新 allData
    updateAllDataCache()

    // 立即显示成功提示
    toast.success(
      `已按照 ${ratios.train}%:${ratios.test}%:${ratios.val}%:${ratios.support}% 的比例划分数据集`,
    )

    // 立即保存重要状态变更
    saveCurrentStateImmediate()
  }

  const handleCustomDatasetConfirm = (newDatasetData: any) => {
    console.log('确认自定义数据集配置:', newDatasetData)

    // 更新数据集数据并重新计算 allData
    state.datasetData.trainData = newDatasetData.trainData || []
    state.datasetData.testData = newDatasetData.testData || []
    state.datasetData.valData = newDatasetData.valData || []
    state.datasetData.supportData = newDatasetData.supportData || []
    state.datasetData.allData = [
      ...state.datasetData.trainData,
      ...state.datasetData.testData,
      ...state.datasetData.valData,
      ...state.datasetData.supportData,
    ]

    // 自定义数据集配置时，重置所有项的推荐状态
    const resetRecommendedStatus = (items: any[]) => {
      items.forEach((item) => {
        item.isRecommended = false
      })
    }

    resetRecommendedStatus(state.datasetData.trainData)
    resetRecommendedStatus(state.datasetData.testData)
    resetRecommendedStatus(state.datasetData.valData)
    resetRecommendedStatus(state.datasetData.supportData)
    resetRecommendedStatus(state.datasetData.allData)

    toast.success('自定义数据集配置已保存')
  }

  const handleChartSettingsUpdate = (itemId: string, startCycle: number, endCycle: number) => {
    // 直接更新对应数据集项中的 startCycle 和 endCycle
    const updateDatasetItem = (items: any[]) => {
      const item = items.find((item) => item.id === itemId)
      if (item) {
        item.startCycle = startCycle
        item.endCycle = endCycle
        return true
      }
      return false
    }

    const updated =
      updateDatasetItem(state.datasetData.trainData) ||
      updateDatasetItem(state.datasetData.testData) ||
      updateDatasetItem(state.datasetData.valData) ||
      updateDatasetItem(state.datasetData.supportData)

    if (updated) {
      console.log(`更新图表设置 - ${itemId}:`, { startCycle, endCycle })
      saveCurrentState()
    }
  }

  // 打开提交对话框
  const openSubmitDialog = () => {
    if (isTrainTaskProcessing.value) {
      toast.warning('训练正在进行中，请稍候')
      return
    }

    // 验证数据集
    if (!state.datasetData.trainData || state.datasetData.trainData.length === 0) {
      toast.error('请先配置训练数据集')
      return
    }

    if (!state.datasetData.valData || state.datasetData.valData.length === 0) {
      toast.error('请先配置验证数据集')
      return
    }

    showSubmitDialog.value = true
  }

  // 处理提交任务
  const handleSubmit = async (serverInfo: any) => {
    try {
      showSubmitDialog.value = false

      // 保存服务器信息
      lastServerInfo.value = {
        server_id: serverInfo.server_id,
        service_name: serverInfo.service_name,
      }

      // console.log('开始训练，服务器信息：', serverInfo)
      // console.log('模型结果配置:', state.modelResult)
      // console.log('训练参数:', state.trainParams)

      // 生成训练所需的数据格式
      const trainingData = generateTrainingData(state.datasetData)
      // console.log('训练数据格式:', trainingData)

      // 构造模型参数 JSON 字符串
      const modelParamsData = {
        model_name: state.modelResult.modelName,
        model_desc: state.modelResult.modelDesc,
        model_params: {
          in_channels: parseInt(state.modelResult.modelParams.inChannels),
          channels: parseInt(state.modelResult.modelParams.channels),
          input_segment_height: parseInt(state.modelResult.modelParams.inputSegmentHeight),
          input_segment_width: parseInt(state.modelResult.modelParams.inputSegmentWidth),
          alpha: parseInt(state.modelResult.modelParams.alpha),
          kernel_size: parseInt(state.modelResult.modelParams.kernelSize),
          train_support_size: parseInt(state.modelResult.modelParams.trainSupportSize),
          test_support_size: parseInt(state.modelResult.modelParams.testSupportSize),
          act_fn: state.modelResult.modelParams.actFn,
          use_fc_for_prediction: state.modelResult.modelParams.useFcForPrediction,
          filter_cycles_flag: state.modelResult.modelParams.filterCyclesFlag,
          features_to_drop: state.modelResult.modelParams.featuresToDrop,
          cycles_to_drop_in_segment: state.modelResult.modelParams.cyclesToDropInSegment,
          return_pointwise_predictions: state.modelResult.modelParams.returnPointwisePredictions,
        },
      }

      // 构造训练参数 JSON 字符串
      const trainParamsData = {
        batch_size: state.trainParams.batchSize,
        epoch: state.trainParams.epoch,
      }

      // 构造输入数据 JSON 字符串
      const inputDatasData = {
        train_files: trainingData.train_data,
        val_files: trainingData.val_data,
        test_files: trainingData.test_data,
        support_files: trainingData.support_data,
      }
      console.log('模型训练提交参数=========', {
        service_name: serverInfo.service_name,
        server_id: serverInfo.server_id,
        is_save: serverInfo.is_save,
        modelParamsData: JSON.stringify(modelParamsData),
        trainParamsData: JSON.stringify(trainParamsData),
        inputDatasData: JSON.stringify(inputDatasData),
      })

      // 调用 eolTrainApi
      const response = await service.eolTrainApi(
        serverInfo.service_name,
        serverInfo.server_id,
        serverInfo.is_save,
        JSON.stringify(modelParamsData),
        JSON.stringify(trainParamsData),
        JSON.stringify(inputDatasData),
        // model_params_data,
        // train_params_data,
        // input_datas_data,
      )
      if (response.statusCode === 200 || response.statusCode === '200') {
        const taskId = response.taskId

        // 保存训练任务ID
        state.trainTask.taskId = taskId

        // 启动任务轮询
        taskStore.startPolling(taskId)

        // 构造任务输入数据
        const taskInputData = {
          model_params: JSON.stringify(modelParamsData),
          train_params: JSON.stringify(trainParamsData),
          input_datas: JSON.stringify(inputDatasData),
        }

        // 更新节点数据
        updateNodeDataUtil(props.nodeData, {
          taskId: taskId,
          taskInputData: taskInputData,
          taskOutputData: null,
        })

        // 保存任务状态
        saveCurrentStateImmediate()

        toast.success('模型训练任务已提交', {
          description: `任务ID: ${taskId}`,
        })

        console.log('训练任务提交成功，任务ID:', taskId)
      } else {
        throw new Error(response.message || '提交任务失败')
      }
    } catch (error: any) {
      console.error('提交训练任务失败：', error)
      toast.error('提交失败', {
        description: error.message || '未知错误',
      })
    }
  }

  // 任务操作配置
  const taskOperations: Record<
    string,
    {
      name: string
      action: (taskId: string) => Promise<any>
      successMessage: string
      successDescription: string
      errorMessage: string
      warningMessage: string
      warningDescription: string
      requiresProcessing: boolean
      afterSuccess?: (taskId: string) => void
    }
  > = {
    pause: {
      name: '暂停',
      action: (taskId: string) => taskService.pauseTask(taskId),
      successMessage: '任务已暂停',
      successDescription: '训练任务已暂停',
      errorMessage: '暂停失败',
      warningMessage: '无法暂停',
      warningDescription: '没有正在进行的任务',
      requiresProcessing: true,
    },
    stop: {
      name: '终止',
      action: (taskId: string) => taskService.stopTask(taskId),
      successMessage: '任务已终止',
      successDescription: '训练任务已成功终止',
      errorMessage: '终止失败',
      warningMessage: '无法终止',
      warningDescription: '没有正在进行的任务',
      requiresProcessing: true,
      afterSuccess: (taskId: string) => taskStore.stopPolling(taskId),
    },
    resume: {
      name: '恢复',
      action: (taskId: string) => taskService.resumeTask(taskId),
      successMessage: '任务已恢复',
      successDescription: '训练任务已恢复运行',
      errorMessage: '恢复失败',
      warningMessage: '无法恢复',
      warningDescription: '没有可恢复的任务',
      requiresProcessing: false,
      afterSuccess: (taskId: string) => taskStore.startPolling(taskId),
    },
  }

  // 通用任务操作处理函数
  const handleTaskOperation = async (operationType: keyof typeof taskOperations) => {
    const operation = taskOperations[operationType]

    // 检查是否需要任务正在处理中
    if (operation.requiresProcessing && !isTrainTaskProcessing.value) {
      return
    }

    const taskId = state.trainTask.taskId
    if (!taskId) {
      toast.warning(operation.warningMessage, {
        description: operation.warningDescription,
      })
      return
    }

    try {
      console.log(`开始${operation.name}任务:`, taskId)
      const result = await operation.action(taskId)

      if (result.status === 'Success') {
        await taskStore.updateTaskList(taskId)

        // 执行成功后的额外操作
        if (operation.afterSuccess) {
          operation.afterSuccess(taskId)
        }

        toast.success(operation.successMessage, {
          description: operation.successDescription,
        })

        saveCurrentStateImmediate()
        console.log(`${operation.name}任务成功:`, taskId)
      } else {
        toast.error(operation.errorMessage, {
          description: result.message || `${operation.name}任务时发生错误`,
        })
      }
    } catch (error: any) {
      console.error(`${operation.name}任务失败:`, error)
      toast.error(operation.errorMessage, {
        description: error.message || `${operation.name}任务时发生错误`,
      })
    }
  }

  // 具体的处理函数
  const handlePause = () => handleTaskOperation('pause')
  const handleStop = () => handleTaskOperation('stop')
  const handleResume = () => handleTaskOperation('resume')

  // 下载导出的文件
  const downloadExportedFile = async (filePath: string) => {
    try {
      console.log('=== downloadExportedFile 开始 ===')
      console.log('文件路径:', filePath)

      // 使用通用的下载模型文件方法
      console.log('调用 downloadModelFile...')
      const success = await downloadModelFile(filePath, (progress, fileName) => {
        console.log(`下载进度: ${progress.toFixed(1)}% - ${fileName}`)
        // 可以在这里更新UI显示下载进度
      })

      console.log('downloadModelFile 返回结果:', success)
      if (!success) {
        console.log('用户取消了下载或下载失败')
      }

      return success
    } catch (error: any) {
      console.error('下载文件失败:', error)
      toast.error('下载失败', {
        description: error.message || '下载文件时发生错误',
      })
      return false
    }
  }

  // 解析导出结果中的文件路径
  const parseFilePath = (result: any): string => {
    if (typeof result === 'string') return result
    if (result?.values?.result) return result.values.result
    if (result?.result) return result.result
    return ''
  }

  // 处理已有导出结果的下载
  const handleExistingResult = async (): Promise<boolean> => {
    const filePath = parseFilePath(state.exportTask.result)

    if (!filePath) {
      toast.error('导出失败', { description: '无法获取导出文件路径' })
      return false
    }

    console.log('准备下载文件:', filePath)
    await downloadExportedFile(filePath)
    return true
  }

  // 验证导出前置条件
  const validateExportConditions = (): string | null => {
    const trainTaskId = state.trainTask.taskId

    if (!trainTaskId) {
      return '没有可导出的训练任务，请先完成模型训练'
    }

    if (state.exportTask.isExporting) {
      return '导出任务正在进行中，请稍候'
    }

    // 检查训练结果中的 epoch 是否大于 0
    if (state.trainTask.result) {
      const epoch = state.trainTask.result.epoch
      if (!epoch || epoch <= 0) {
        return '请等待训练结果完成，当前训练轮数不足'
      }
    } else {
      return '请等待训练结果完成'
    }

    return null
  }

  // 提交新的导出任务
  const submitExportTask = async (trainTaskId: string): Promise<void> => {
    state.exportTask.isExporting = true

    const response = await service.exportEoLModelApi(trainTaskId)

    if (response.statusCode !== 200 && response.statusCode !== '200') {
      throw new Error(response.message || '提交导出任务失败')
    }

    const exportTaskId = response.taskId
    state.exportTask.taskId = exportTaskId
    taskStore.startPolling(exportTaskId)
    saveCurrentStateImmediate()

    toast.success('导出任务已提交', { description: `任务ID: ${exportTaskId}` })
    console.log('导出任务提交成功，任务ID:', exportTaskId)
  }

  const handleExport = async () => {
    console.log('=== handleExport 开始 ===')

    try {
      // 如果已有导出结果，直接下载
      if (state.exportTask.result) {
        console.log('检测到导出结果，开始下载')
        await handleExistingResult()
        return
      }

      // 验证导出条件
      const validationError = validateExportConditions()
      if (validationError) {
        const isWarning = validationError.includes('正在进行中')
        const toastFn = isWarning ? toast.warning : toast.error
        const title = isWarning ? '导出中' : '导出失败'

        toastFn(title, { description: validationError })
        return
      }

      // 提交导出任务
      const trainTaskId = state.trainTask.taskId!
      // const trainTaskId = '0::eolTrain::6SaGYakwEq5ozMN6KUCUXN'

      await submitExportTask(trainTaskId)
    } catch (error: any) {
      console.error('导出操作失败:', error)
      state.exportTask.isExporting = false
      toast.error('导出失败', {
        description: error.message || '导出过程中发生未知错误',
      })
    }
  }

  // 监听器设置
  const setupWatchers = () => {
    // 监听 trainModelConfig 变化
    watch(
      trainModelConfig,
      (newConfig) => {
        if (newConfig && (newConfig.modelResult || newConfig.model_params)) {
          const savedParams = getNodeParams(props.nodeData)
          const hasUserModified = savedParams?.modelParamsModified === true

          if (hasUserModified && savedParams?.modelResult) {
            console.log('用户已修改过参数，使用存储的配置')
            Object.assign(state.modelResult, savedParams.modelResult)
            if (savedParams.datasetData) {
              Object.assign(state.datasetData, savedParams.datasetData)
            }
          } else {
            state.modelResult = updateModelResultFromConfig(
              newConfig,
              state.modelResult,
              hasUserModified,
            )
          }
        }
      },
      { immediate: true, deep: true },
    )

    // 监听 trainInputData 变化
    watch(
      trainInputData,
      (newInputData) => {
        state.checkInputBatteryData.inputDatas = newInputData
        if (newInputData) {
          checkInputBatteryData(newInputData)
        }
      },
      { immediate: true },
    )

    // 监听任务状态变化
    watch(
      () =>
        state.checkInputBatteryData.taskId
          ? taskStore.getTaskStatus(state.checkInputBatteryData.taskId).value
          : null,
      async (newStatus) => {
        if (!state.checkInputBatteryData.taskId || !newStatus) return

        if (['Finished', 'Error', 'Abort'].includes(newStatus)) {
          await taskStore.updateTaskResult(state.checkInputBatteryData.taskId)
          const result = taskStore.getTaskResultById(state.checkInputBatteryData.taskId).value
          state.checkInputBatteryData.result = result

          window.logger.info(
            'checkInputBatteryData 任务完成，结果:',
            JSON.parse(JSON.stringify(state)),
          )
          updateDatasetFromApiResult()
        }
      },
    )

    // 监听训练任务结果变化
    watch(
      () => {
        const taskId = state.trainTask.taskId
        if (!taskId) return null

        // 直接监听 taskResultsMap 中的结果变化
        return taskStore.getTaskResultById(taskId).value
      },
      (newResult, oldResult) => {
        if (!state.trainTask.taskId || !newResult) return

        // 只有当结果真正发生变化时才处理
        if (newResult !== oldResult) {
          console.log('监听到训练任务结果更新:', newResult)

          // 直接使用从 taskStore 获取的解析后的结果
          state.trainTask.result = newResult

          // 保存状态
          saveCurrentState()
        }
      },
      { deep: true },
    )

    // 监听训练任务状态变化（处理任务完成通知）
    watch(
      () => (state.trainTask.taskId ? taskStore.getTaskStatus(state.trainTask.taskId).value : null),
      async (newStatus, oldStatus) => {
        if (!state.trainTask.taskId || !newStatus) return

        console.log('监听训练任务状态变化:', { newStatus, oldStatus })

        // 只在状态真正发生变化时处理完成通知
        if (newStatus !== oldStatus && ['Finished', 'Error', 'Abort'].includes(newStatus)) {
          // 任务完成时，确保获取最新的结果
          await taskStore.updateTaskResult(state.trainTask.taskId)
          const latestResult = taskStore.getTaskResultById(state.trainTask.taskId).value

          if (latestResult) {
            state.trainTask.result = latestResult
            console.log('任务完成，获取到最新结果:', latestResult)
          }

          if (newStatus === 'Finished') {
            toast.success('模型训练完成', {
              description: '训练任务已成功完成',
            })

            // 检查训练结果中的 epoch 是否大于 0，如果是则自动提交导出任务
            if (state.trainTask.result && state.trainTask.result.epoch > 0) {
              console.log('训练完成且 epoch > 0，自动提交导出任务')
              try {
                const trainTaskId = state.trainTask.taskId!
                await submitExportTask(trainTaskId)
              } catch (error: any) {
                console.error('自动提交导出任务失败:', error)
                toast.error('自动导出失败', {
                  description: error.message || '自动提交导出任务时发生错误',
                })
              }
            }
          } else if (newStatus === 'Error') {
            toast.error('模型训练失败', {
              description: '训练任务执行出错',
            })
          } else if (newStatus === 'Abort') {
            toast.warning('模型训练已终止', {
              description: '训练任务被用户终止',
            })
          }

          // 保存最终状态
          saveCurrentStateImmediate()
          console.log('训练任务完成，最终结果:', state.trainTask.result)
        }
      },
    )

    // 监听导出任务结果变化
    watch(
      () => {
        const taskId = state.exportTask.taskId
        if (!taskId) return null

        // 直接监听 taskResultsMap 中的结果变化
        return taskStore.getTaskResultById(taskId).value
      },
      (newResult, oldResult) => {
        if (!state.exportTask.taskId || !newResult) return

        // 只有当结果真正发生变化时才处理
        if (newResult !== oldResult) {
          console.log('监听到导出任务结果更新:', newResult)

          // 直接使用从 taskStore 获取的解析后的结果
          state.exportTask.result = newResult

          // 保存状态
          saveCurrentState()
        }
      },
      { deep: true },
    )

    // 监听导出任务状态变化
    watch(
      () =>
        state.exportTask.taskId ? taskStore.getTaskStatus(state.exportTask.taskId).value : null,
      async (newStatus, oldStatus) => {
        if (!state.exportTask.taskId || !newStatus) return

        console.log('监听导出任务状态变化:', { newStatus, oldStatus })

        // 只在状态真正发生变化时处理完成通知
        if (newStatus !== oldStatus && ['Finished', 'Error', 'Abort'].includes(newStatus)) {
          // 任务完成时，确保获取最新的结果
          await taskStore.updateTaskResult(state.exportTask.taskId)
          const latestResult = taskStore.getTaskResultById(state.exportTask.taskId).value

          if (latestResult) {
            state.exportTask.result = latestResult
            console.log('导出任务完成，获取到最新结果:', latestResult)
          }

          // 重置导出状态
          state.exportTask.isExporting = false

          if (newStatus === 'Finished') {
            // 检查导出结果
            const task = taskStore.tasks.find((t) => t.taskId === state.exportTask.taskId)
            if (task?.taskLog && task.taskLog.includes('Error')) {
              toast.error('模型导出失败', {
                description: '导出过程中出现错误，请查看任务日志',
              })
            } else if (latestResult) {
              // 解析文件路径
              let filePath = ''
              if (typeof latestResult === 'string') {
                filePath = latestResult
              } else if (latestResult.values && latestResult.values.result) {
                filePath = latestResult.values.result
              } else if (latestResult.result) {
                filePath = latestResult.result
              }

              if (filePath) {
                toast.success('模型导出完成', {
                  description: `导出文件路径: ${filePath}`,
                })

                // 导出完成后，自动触发下载
                try {
                  await downloadExportedFile(filePath)
                } catch (error: any) {
                  console.error('自动下载导出文件失败:', error)
                  toast.error('下载失败', {
                    description: '导出完成但下载失败，请手动重试',
                  })
                }
              } else {
                toast.success('模型导出完成', {
                  description: '导出任务已成功完成',
                })
                console.log('无法解析导出文件路径，结果:', latestResult)
              }
            } else {
              toast.success('模型导出完成', {
                description: '导出任务已成功完成',
              })
            }
          } else if (newStatus === 'Error') {
            toast.error('模型导出失败', {
              description: '导出任务执行出错',
            })
          } else if (newStatus === 'Abort') {
            toast.warning('模型导出已终止', {
              description: '导出任务被用户终止',
            })
          }

          // 保存最终状态
          saveCurrentStateImmediate()
          console.log('导出任务完成，最终结果:', state.exportTask.result)
        }
      },
    )

    // 使用浅层监听和选择性监听，减少深度响应式开销
    watch(() => state.modelResult, saveCurrentState, { deep: false })
    watch(
      () => [
        state.datasetData.trainData.length,
        state.datasetData.testData.length,
        state.datasetData.valData.length,
        state.datasetData.supportData.length,
      ],
      saveCurrentState,
    )
    watch(() => state.datasetRatios, saveCurrentState, { deep: false })
    watch(() => state.trainParams, saveCurrentState, { deep: false })
    watch(() => state.exportTask, saveCurrentState, { deep: false })
  }

  // 初始化时恢复状态
  restoreStateFromStorage()

  return {
    state,
    showDatasetRatioDialog,
    showCustomDatasetDialog,
    showSubmitDialog,
    isLoadingBatteryData,
    trainModelConfig,
    trainInputData,
    trainTaskProgress,
    isTrainTaskProcessing,
    trainTaskStatus,
    trainTaskCompleted,
    trainTaskDuration,
    canShowExportButton,
    lastServerInfo,

    // 方法
    handleExport: useDebounceFn(handleExport, 1000),
    downloadExportedFile,
    handlePause,
    handleStop,
    handleResume,
    handleUseRecommendedDataset,
    handleOpenCustomDataset,
    handleDatasetRatioConfirm,
    handleCustomDatasetConfirm,
    handleChartSettingsUpdate,
    restoreStateFromStorage,
    setupWatchers,
    openSubmitDialog,
    handleSubmit,

    // 使用计算属性缓存，避免重复计算
    getTotalDataCount: computed(() => {
      if (state.checkInputBatteryData.result && Array.isArray(state.checkInputBatteryData.result)) {
        return state.checkInputBatteryData.result.length
      }
      return 0
    }),
  }
}
