const path = require('path')
const fs = require('fs').promises
import logger from '../utils/logger'
// const grpc = require('@grpc/grpc-js')
import * as grpc from '@grpc/grpc-js'
const protoLoader = require('@grpc/proto-loader')
// 封装思路，获取存根
interface FsReaddir {
  // fs.readdir 的返回值类型,用到的属性
  name: string // 文件名称
  path: string // 文件路径
  isDirectory: () => boolean // 是否是目录
}
interface GrpcError {
  code: grpc.status
  message: string
  details?: string
}
interface GrpcCallOptions {
  timeout?: number
  metadata?: grpc.Metadata
  onData?: (data: any) => void
  onEnd?: () => void
  onError?: (error: GrpcError) => void
  onStatus?: (status: grpc.StatusObject) => void
}

// 注册服务
class GrpcClient {
  // public async getStub() {
  //   const dirPath = path.join(__dirname, 'protos')
  //   // const protoFiles = await this.getAllProtoFiles(dirPath)
  // }
  private async getAllProtoFiles(dirPath: string) {
    let results: string[] = []
    try {
      const items: FsReaddir[] = await fs.readdir(dirPath, { withFileTypes: true })
      for (let i = 0; i < items.length; i++) {
        const item: FsReaddir = items[i]
        const fullPath: string = path.join(dirPath, item.name)
        if (item.isDirectory()) {
          results = results.concat(await this.getAllProtoFiles(fullPath))
        } else {
          results.push(fullPath)
        }
      }
    } catch (error) {
      logger.info(`Error reading directory ${dirPath}:${error}`)
    }
    return results
  }
  /**
   * 加载并解析.proto文件
   * @param {string[]} protoFiles .proto文件路径数组
   * @returns {Promise<Object>} gRPC包定义
   */
  private async loadProtoDefinitions(protoFiles) {
    if (protoFiles.length === 0) {
      throw new Error('No .proto files provided')
    }

    const includeDirs = [
      ...new Set(protoFiles.map((file) => path.dirname(file))), // 获取所有不重复的目录
    ]
    const options = {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
      includeDirs,
    }

    try {
      const packageDefinition = await protoLoader.load(protoFiles, options)
      return grpc.loadPackageDefinition(packageDefinition)
    } catch (error) {
      logger.info(`Error loading proto definitions:${error}`)
      throw error
    }
  }
  /**
   * 注册所有找到的gRPC服务
   * @param {Object} packageDefinition 从proto加载的包定义
   * @param {string} serverAddress 服务端地址，如'localhost:50051'
   */
  private services: any = {}
  private methodsInservices: any = new Map()
  private DEFAULT_OPTIONS = {
    // 默认配置
    'grpc.max_send_message_length': 10 * 1024 * 1024, // 最大发送消息大小 10MB
    'grpc.max_receive_message_length': 10 * 1024 * 1024, // 最大接收消息大小 10MB
  }
  private registerAllServices(packageDefinition: any, serverAddress: any, options?: any) {
    // 清除现有服务
    this.services = {}
    this.methodsInservices.clear()

    // 遍历包定义中的所有服务
    for (const [packageName, packageContent] of Object.entries(packageDefinition)) {
      if (typeof packageContent !== 'object') continue
      for (const [serviceName, ServiceClient] of Object.entries(packageContent as any)) {
        // 检查是否为有效的服务客户端
        const serviceClient: any = ServiceClient
        if (serviceClient && serviceClient.service && typeof serviceClient.service === 'object') {
          const fullServiceName = `${packageName}.${serviceName}`
          let opts = this.DEFAULT_OPTIONS
          if (options) {
            opts = {
              ...this.DEFAULT_OPTIONS,
              ...options,
            }
          }
          this.services[fullServiceName] = new serviceClient(
            serverAddress,
            grpc.credentials.createInsecure(),
            opts,
          )
          for (const [methodName, method] of Object.entries(serviceClient.service)) {
            const md: any = method
            // 查看不同服务中的方法是否有重复
            const key: string = `${fullServiceName}.${methodName}`
            this.methodsInservices.set(key, {
              fullServiceName,
              requestStream: md.requestStream,
              responseStream: md.responseStream,
            })
          }
        }
      }
    }
  }
  /**
   * 获取指定服务的客户端
   * @param {string} serviceName 服务名称(格式: packageName.ServiceName)
   * @returns {Object} gRPC客户端实例
   */
  public getServiceClient(serviceName) {
    const client = this.services[serviceName]
    if (!client) {
      throw new Error(`Service ${serviceName} not found`)
    }
    return client
  }
  /**
   * @param {string} serviceName 服务名称(格式: packageName.ServiceName.methodName)
   */
  public getMethods(methodName) {
    // 防止在开发过程中出现不同服务中有同名方法的情况
    const service = this.methodsInservices.get(methodName) // 根据保存的 [存根.服务名.方法名] 获取保存的服务名称和方法信息
    if (!service) {
      throw new Error(`Method ${methodName} not found`)
    }
    const nameList = methodName.split('.')
    if (nameList.length !== 3) {
      throw new Error(`Invalid method name ${methodName}`)
    }
    const mName = nameList[2]
    const method = this.services[service.fullServiceName][mName]
    if (typeof method !== 'function') {
      throw new Error(`Method ${methodName} not found in service`)
    }
    return method
  }
  // private packageDefinitions: any = {} // 存储加载的proto定义
  /**
   * 初始化gRPC服务管理器
   * @param {string} protoDir .proto文件所在目录
   * @param {string} serverAddress gRPC服务端地址
   */
  public async initialize(serverAddress: string, options?: Record<string, any>) {
    const dirPath = path.join(__dirname, 'protos') // proto文件所在目录
    try {
      // 1. 获取所有.proto文件
      const protoFiles = await this.getAllProtoFiles(dirPath)

      if (protoFiles.length === 0) {
        throw new Error('No .proto files found in the specified directory')
      }

      // 2. 加载proto定义
      const packageDefinition = await this.loadProtoDefinitions(protoFiles)
      // this.packageDefinitions = packageDefinition
      // 3. 注册所有服务
      this.registerAllServices(packageDefinition, serverAddress, options)

      return { success: true, services: Object.keys(this.services) }
    } catch (error: any) {
      console.error('Initialization failed:', error)
      return { success: false, error: error.message }
    }
  }
}
interface ClientOptions {
  serverAddress: string
  options?: Record<string, any>
}
class GrpcClientWrapper {
  private static initOptions: ClientOptions
  constructor(options: ClientOptions) {
    // this.initialize(options.serverAddress, options.options)
    GrpcClientWrapper.initOptions = options
  }
  // 使用单例模式，避免重复创建客户端
  private static instance: GrpcClientWrapper // serverAddress: string, options?: Record<string, any>
  public static getInstance(): GrpcClientWrapper {
    if (!GrpcClientWrapper.instance) {
      GrpcClientWrapper.instance = new GrpcClientWrapper(this.initOptions)
    }
    return GrpcClientWrapper.instance
  }
  private static grpcClient: GrpcClient = new GrpcClient()
  public static async initialize(): Promise<any> {
    const result = await this.grpcClient.initialize(
      this.initOptions.serverAddress,
      this.initOptions.options,
    )
    return result
  }
  /**
   * 一元调用 (Unary)
   */
  public static async unaryCall<TRequest, TResponse>(
    serviceName: string,
    methodName: string,
    request: TRequest,
    options: Omit<GrpcCallOptions, 'onData' | 'onEnd' | 'onStatus' | 'onError'> = {},
  ): Promise<TResponse> {
    const client = this.grpcClient.getServiceClient(serviceName)
    const method = this.grpcClient.getMethods(serviceName + '.' + methodName)

    return new Promise<TResponse>((resolve, reject) => {
      const call = method.call(
        client,
        request,
        options.metadata || new grpc.Metadata(),
        { deadline: this.getDeadline(options.timeout) },
        (error: grpc.ServiceError | null, response: TResponse) => {
          if (error) {
            reject(this.formatError(error))
          } else {
            resolve(response)
          }
        },
      )

      this.setupCallEvents(call, options)
    })
  }
  /**
   * 服务器流 (Server Streaming)
   */
  public static serverStreaming<TRequest, TResponse>(
    serviceName: string,
    methodName: string,
    request: TRequest,
    options: GrpcCallOptions = {},
  ): grpc.ClientReadableStream<TResponse> {
    const client = this.grpcClient.getServiceClient(serviceName)
    const method = this.grpcClient.getMethods(serviceName + '.' + methodName)

    const call = method.call(client, request, options.metadata || new grpc.Metadata(), {
      deadline: this.getDeadline(options.timeout),
    })

    this.setupCallEvents(call, options)
    return call
  }
  /**
   * 客户端流 (Client Streaming)
   */
  public static clientStreaming<TRequest, TResponse>(
    serviceName: string,
    methodName: string,
    options: GrpcCallOptions = {},
  ): grpc.ClientWritableStream<TRequest> {
    const client = this.grpcClient.getServiceClient(serviceName)
    const method = this.grpcClient.getMethods(serviceName + '.' + methodName)

    const call = method.call(client, options.metadata || new grpc.Metadata(), {
      deadline: this.getDeadline(options.timeout),
    })

    this.setupCallEvents(call, options)
    return call as grpc.ClientWritableStream<TRequest>
  }
  /**
   * 双向流 (Bidirectional Streaming)
   */
  public static bidirectionalStreaming<TRequest, TResponse>(
    serviceName: string,
    methodName: string,
    options: GrpcCallOptions = {},
  ): grpc.ClientDuplexStream<TRequest, TResponse> {
    const client = this.grpcClient.getServiceClient(serviceName)
    const method = this.grpcClient.getMethods(serviceName + '.' + methodName)

    const call = method.call(client, options.metadata || new grpc.Metadata(), {
      deadline: this.getDeadline(options.timeout),
    })

    this.setupCallEvents(call, options)
    return call
  }
  /**
   * 处理请求事件
   * @param call grpc调用实例
   * @param options grpc调用选项
   */
  private static setupCallEvents(
    call:
      | grpc.ClientReadableStream<any>
      | grpc.ClientWritableStream<any>
      | grpc.ClientDuplexStream<any, any>,
    options: GrpcCallOptions,
  ): void {
    if (options.timeout) {
      setTimeout(() => {
        if (!call.destroyed) {
          call.cancel()
        }
      }, options.timeout)
    }

    call.on('data', (data: any) => {
      if (options.onData) options.onData(data)
    })

    call.on('end', () => {
      if (options.onEnd) options.onEnd()
    })

    call.on('error', (error: grpc.ServiceError) => {
      if (options.onError) options.onError(this.formatError(error))
    })

    call.on('status', (status: grpc.StatusObject) => {
      if (options.onStatus) options.onStatus(status)
    })
  }
  private static formatError(error: grpc.ServiceError): GrpcError {
    return {
      code: error.code || grpc.status.UNKNOWN,
      message: error.message,
      details: error.details,
    }
  }

  private static getDeadline(timeoutMs?: number): Date | undefined {
    return timeoutMs ? new Date(Date.now() + timeoutMs) : undefined
  }
}

export default GrpcClientWrapper
