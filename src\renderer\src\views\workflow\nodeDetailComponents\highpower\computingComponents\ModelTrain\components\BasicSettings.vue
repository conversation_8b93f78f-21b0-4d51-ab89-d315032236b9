<template>
  <Card class="my-4 shadow-sm">
    <CardHeader class="py-2">
      <Collapsible :default-open="true">
        <CollapsibleTrigger class="w-full">
          <div class="flex items-center justify-between">
            <CardTitle class="text-sm font-medium">基本参数配置</CardTitle>

            <div
              class="flex items-center text-xs text-muted-foreground hover:text-foreground transition-colors"
            >
              <span class="mr-1">设置</span>
              <LucideIcon
                name="ChevronDown"
                class="h-4 w-4 transition-transform duration-200 [&[data-state=open]>svg]:rotate-180"
              />
            </div>
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent class="py-2 mt-2">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div class="flex flex-col space-y-2">
                <Label for="inChannels" class="text-sm font-medium">输入通道数</Label>
                <Input
                  id="inChannels"
                  v-model="localParams.inChannels"
                  type="number"
                  min="1"
                  placeholder="请输入输入通道数"
                  class="h-10 w-full"
                  :disabled="props.disabled"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="channels" class="text-sm font-medium">通道数</Label>
                <Input
                  id="channels"
                  v-model="localParams.channels"
                  type="number"
                  min="1"
                  placeholder="请输入通道数"
                  class="h-10 w-full"
                  :disabled="props.disabled"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentHeight" class="text-sm font-medium">输入片段高度</Label>
                <Input
                  id="inputSegmentHeight"
                  v-model="localParams.inputSegmentHeight"
                  type="number"
                  min="1"
                  placeholder="请输入输入段高度"
                  class="h-10 w-full"
                  :disabled="props.disabled"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="inputSegmentWidth" class="text-sm font-medium">输入片段宽度</Label>
                <Input
                  id="inputSegmentWidth"
                  v-model="localParams.inputSegmentWidth"
                  type="number"
                  min="1"
                  placeholder="请输入输入段宽度"
                  class="h-10 w-full"
                  :disabled="props.disabled"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="alpha" class="text-sm font-medium">系数</Label>
                <Input
                  id="alpha"
                  v-model="localParams.alpha"
                  type="number"
                  step="0.1"
                  placeholder="请输入alpha值"
                  class="h-10 w-full"
                  :disabled="props.disabled"
                />
              </div>

              <div class="flex flex-col space-y-2">
                <Label for="kernelSize" class="text-sm font-medium">核大小</Label>
                <Input
                  id="kernelSize"
                  v-model="localParams.kernelSize"
                  type="number"
                  min="1"
                  placeholder="请输入核大小"
                  class="h-10 w-full"
                  :disabled="props.disabled"
                />
              </div>

              <!-- 更多参数区域 -->
              <template v-if="showMoreParams">
                <div class="flex flex-col space-y-2">
                  <Label for="trainSupportSize" class="text-sm font-medium">训练支持集大小</Label>
                  <Input
                    id="trainSupportSize"
                    v-model="localParams.trainSupportSize"
                    type="number"
                    min="1"
                    placeholder="请输入训练支持大小"
                    class="h-10 w-full"
                    :disabled="props.disabled"
                  />
                </div>

                <div class="flex flex-col space-y-2">
                  <Label for="testSupportSize" class="text-sm font-medium">测试支持集大小</Label>
                  <Input
                    id="testSupportSize"
                    v-model="localParams.testSupportSize"
                    type="number"
                    min="1"
                    placeholder="请输入测试支持大小"
                    class="h-10 w-full"
                    :disabled="props.disabled"
                  />
                </div>

                <div class="flex flex-col space-y-2">
                  <Label for="actFn" class="text-sm font-medium">激活函数</Label>
                  <Select
                    id="actFn"
                    v-model="localParams.actFn.default"
                    class="w-full"
                    :disabled="props.disabled"
                  >
                    <SelectTrigger class="h-10">
                      <SelectValue placeholder="请选择激活函数" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="choice in localParams.actFn?.choices || []"
                        :key="choice"
                        :value="choice"
                      >
                        {{ choice }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <!-- 布尔值参数 -->
                <div class="flex flex-col space-y-2">
                  <Label class="text-sm font-medium">使用全连接层进行预测</Label>
                  <div class="flex items-center space-x-2">
                    <input
                      id="useFcForPrediction"
                      v-model="localParams.useFcForPrediction"
                      type="checkbox"
                      class="h-4 w-4"
                      :disabled="props.disabled"
                    />
                    <Label for="useFcForPrediction" class="text-sm">启用FC预测</Label>
                  </div>
                </div>

                <div class="flex flex-col space-y-2">
                  <Label class="text-sm font-medium">过滤周期标志</Label>
                  <div class="flex items-center space-x-2">
                    <input
                      id="filterCyclesFlag"
                      v-model="localParams.filterCyclesFlag"
                      type="checkbox"
                      class="h-4 w-4"
                      :disabled="props.disabled"
                    />
                    <Label for="filterCyclesFlag" class="text-sm">过滤循环标志</Label>
                  </div>
                </div>

                <div class="flex flex-col space-y-2">
                  <Label class="text-sm font-medium">返回逐点预测结果</Label>
                  <div class="flex items-center space-x-2">
                    <input
                      id="returnPointwisePredictions"
                      v-model="localParams.returnPointwisePredictions"
                      type="checkbox"
                      class="h-4 w-4"
                      :disabled="props.disabled"
                    />
                    <Label for="returnPointwisePredictions" class="text-sm">返回逐点预测</Label>
                  </div>
                </div>

                <!-- 字符串参数 -->
                <div class="flex flex-col space-y-2">
                  <Label for="featuresToDrop" class="text-sm font-medium">待丢弃特征</Label>
                  <Input
                    id="featuresToDrop"
                    v-model="localParams.featuresToDrop"
                    placeholder="请输入要丢弃的特征"
                    class="h-10 w-full"
                    :disabled="props.disabled"
                  />
                </div>

                <div class="flex flex-col space-y-2">
                  <Label for="cyclesToDropInSegment" class="text-sm font-medium">
                    片段中待丢弃的周期数
                  </Label>
                  <Input
                    id="cyclesToDropInSegment"
                    v-model="localParams.cyclesToDropInSegment"
                    placeholder="请输入段中要丢弃的循环"
                    class="h-10 w-full"
                    :disabled="props.disabled"
                  />
                </div>
              </template>
            </div>

            <!-- 展开/收缩更多参数的文字链接 -->
            <div class="mt-4 flex justify-end">
              <div
                class="flex items-center space-x-2 text-sm cursor-pointer transition-colors"
                @click="toggleMoreParams"
              >
                <span>{{ showMoreParams ? '收起更多' : '展开更多' }}</span>
                <LucideIcon
                  :name="showMoreParams ? 'ChevronUp' : 'ChevronDown'"
                  class="h-4 w-4 transition-transform duration-200"
                />
                <Badge variant="secondary" class="text-xs">
                  {{ showMoreParams ? totalParams : hiddenParamsCount }}
                </Badge>
              </div>
            </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </CardHeader>
  </Card>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { LucideIcon } from '@renderer/components'
import { useBasicSettings } from '../composables/useBasicSettings'

const modelParams = defineModel('modelParams', { type: Object, required: true })

// 接收 disabled 属性
const props = defineProps<{
  disabled?: boolean
}>()

// 使用组合式函数
const { showMoreParams, totalParams, hiddenParamsCount, toggleMoreParams } = useBasicSettings()

// 创建本地响应式副本以减少父组件更新频率
const localParams = ref({ ...modelParams.value })

console.log('localParams=================>>', localParams)

// 防抖更新父组件
const debouncedUpdate = useDebounceFn(() => {
  Object.assign(modelParams.value, localParams.value)
}, 300)

// 监听本地参数变化
watch(localParams, debouncedUpdate, { deep: true })

// 监听父组件参数变化（用于外部更新）
watch(
  modelParams,
  (newValue) => {
    if (JSON.stringify(newValue) !== JSON.stringify(localParams.value)) {
      localParams.value = { ...newValue }
    }
  },
  { deep: true },
)
</script>

<style></style>
