<template>
  <div>
    <BsCardVue :title="title">
      <template #actions>
        <Button
          v-if="!isShowUpload"
          class="h-[28px]"
          variant="destructive"
          size="sm"
          @click="resetUpload"
        >
          <span>重新上传</span>
        </Button>
      </template>
      <div v-if="isShowUpload" class="relative">
        <div v-if="uploadIcon" class="w-full h-full absolute top-0 left-0 z-50"></div>
        <FileDropUpload
          ref="fileDropUpload"
          :progress-control="true"
          :accept-types="acceptTypes"
          :max-size="maxSize"
          @file-selected="(file) => handleFile(file)"
        >
          <template #title>
            <div class="text-lg mb-2 font-medium">{{ uploadTitle }}</div>
          </template>
          <template #description>
            <div class="text-sm text-gray-500">
              {{ uploadDisc }}
            </div>
          </template>
          <template #icon>
            <svg
              v-if="!uploadIcon"
              class="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              ></path>
            </svg>

            <Loader2 v-if="uploadIcon" class="w-10 h-10 animate-spin m-auto mb-4" />
          </template>
        </FileDropUpload>
      </div>
      <div v-else>
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm mb-4">
          <div>
            <h3 class="text-lg font-bold line-clamp-1 p-4">
              <span>模型名称</span>
            </h3>
            <div class="p-4 pt-0">
              <Input :model-value="modelName" type="text" disabled />
              <!-- <Select v-model="modelSelectValue" class="w-full" @update:model-value="selectChange">
                <SelectTrigger>
                  <SelectValue placeholder="模型选择" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem v-for="(item, index) in modelList" :key="index" :value="item.value">
                      {{ item.label }}
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select> -->
            </div>
          </div>
        </div>
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
          <div class="p-4">
            <h3 class="text-lg font-bold line-clamp-1 mb-4">
              <span>模型信息</span>
            </h3>
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm px-4 py-2">
              <h4 class="text-base font-bold line-clamp-1">
                <span>模型描述</span>
              </h4>
              <div>
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ modelDesc }}
                </span>
              </div>
            </div>
            <div class="mt-4 rounded-lg border bg-card text-card-foreground shadow-sm px-4 py-2">
              <h4 class="text-base font-bold line-clamp-1">
                <span>参数信息</span>
              </h4>
              <div class="grid gap-x-2 grid-cols-2">
                <div v-for="(item, index) in paramList" :key="index" class="grow mt-2">
                  <div class="text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1">
                    <span :title="item.name">{{ item.name }}</span>
                  </div>
                  <div class="mt-2">
                    <Input :model-value="item.value" type="text" disabled />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BsCardVue>
  </div>
</template>
<script setup lang="ts">
import { FileDropUpload } from '@renderer/components'
import { FileService } from '@renderer/config/api/grpc/fileService'
import { createStudyService } from '@renderer/config/api/grpc/studyService'
import { createTaskService } from '@renderer/config/api/grpc/taskService'
import { getNodeParams, saveNodeParams } from '@renderer/utils/nodeUtils'
import { Loader2 } from 'lucide-vue-next'
import { onMounted, onUnmounted, ref, Ref, computed } from 'vue'
import BsCardVue from '../components/BsCard.vue'

import { useFlowsStore, useTaskStore } from '@renderer/store'

const flowsStore = useFlowsStore()
const taskService = createTaskService() // 请求服务
const props = defineProps({
  nodeData: {
    type: Object,
    required: true,
  },
})
const service = createStudyService() // 请求服务
const title: string = '模型配置'
// 获取机器模型训练地址
const modelTrainPath = computed(() => {
  if (props.nodeData?.id && props.nodeData?.data?.workflowId) {
    const params = flowsStore.getNodeParams(props.nodeData.data.workflowId, props.nodeData.id)
    const exportPath = params?.ModelTrain?.exportTask?.result?.values?.result
    return exportPath || {}
  }
  return {}
})
console.log('ModelTrain==============>', modelTrainPath.value)

onMounted(() => {
  initModelData()

  // getModelByPath('1/oDGsKpUJnk_EoL.ml')
  getModelByPath(modelTrainPath.value)
})
const modelData: any = {
  progress: ref(0),
}
const initModelData = () => {
  // 模型数据初始化
  const currentParams = getNodeParams(props.nodeData)
  const data: any = JSON.parse(currentParams.modelResult || '{}')
  path = currentParams.modelPath

  // 初始化进度条
  // console.log('进度条', currentParams.modelPro)
  if (currentParams.modelPro) {
    fileName = currentParams?.fileName || ''
    changeUpload(true)
    changeUplaodProgress(currentParams.modelPro)
  }
  if (currentParams.modelResult) {
    const modelInfo = data
    isShowUpload.value = false
    modelName.value = modelInfo.name
    modelDesc.value = modelInfo.description
    if (modelInfo.model_params) {
      paramList.value = Object.entries(modelInfo.model_params).map(([name, value]) => {
        const valObj: any = value
        if (typeof value === 'object') {
          return { name, value: String(valObj?.default) }
        } else {
          return { name, value: String(value) }
        }
      })
    }
  }

  // 初始化传递参数
  saveNextParams({
    fileName: fileName,
    modelPro: modelData.progress.value,
    modelPath: path,
    modelResult: currentParams.modelResult,
  })
}

const acceptTypes: string[] = ['.ml']
const maxSize: number = 1024
const isShowUpload: Ref<boolean> = ref(true)
const fileService = new FileService()
let fileName: string = ''
const handleFile = (file: File) => {
  fileName = file.name
  handModelFile(file)
}
const uploadTitle: Ref<string> = ref('点击或拖拽上传文件')
const uploadDisc: Ref<string> = ref(`支持 ${acceptTypes.join(', ')} 格式`)
const uploadIcon: Ref<any> = ref(false)
const changeUpload = (bool: boolean) => {
  if (bool) {
    uploadTitle.value = fileName + '上传中...'
    uploadDisc.value = '请等待上传过程完成，再继续其他操作。'
  } else {
    uploadTitle.value = '点击或拖拽上传文件'
    uploadDisc.value = `支持 ${acceptTypes.join(', ')} 格式`
  }
  uploadIcon.value = bool
}
const fileDropUpload: Ref<any> = ref(null)
const changeUplaodProgress = (progress: number) => {
  if (fileDropUpload.value) {
    modelData.progress.value = progress
    saveNextParams({
      fileName: fileName,
      modelPro: modelData.progress.value,
    })
    fileDropUpload.value.updateProgress(progress)
  }
}
let path: string = ''
let isError: boolean = false
const handModelFile = async (file: File) => {
  changeUplaodProgress(2)
  changeUpload(true)
  path = ''
  await fileService.uploadFile(
    file,
    (data) => {
      // 后台最终返回结果
      changeUplaodProgress(80)
      if (data?.data?.stored_filepath) {
        if (!path) {
          path = data.data.stored_filepath
          getModelByPath(path)
        }
      }
      if (!isError) {
        if (data.type === 'error') {
          changeUpload(false)
        }
        isError = true
      }
    },
    (progress) => {
      // 前端推送进度
      const pro = 2 + Math.floor(progress / (100 / 50))
      changeUplaodProgress(pro)
    },
  )
}
let intervalTimer: any = null
const startLoop = (id: any) => {
  if (intervalTimer) {
    clearInterval(intervalTimer)
  }
  taskService.getTaskResult(id).then((res: any) => {
    if (res.result) {
      handleTaskResult(res)
    }
  })
  intervalTimer = setInterval(async () => {
    const res = await taskService.getTaskResult(id)
    if (res.result) {
      handleTaskResult(res)
    }
  }, 1000)
}
const handleTaskResult = (res: any) => {
  endLoop()
  const data = JSON.parse(res.result)
  if (data?.values?.result) {
    const dataObj = JSON.parse(data.values.result)
    const modelInfo = dataObj
    isShowUpload.value = false
    modelName.value = modelInfo.name
    modelDesc.value = modelInfo.description
    if (modelInfo.model_params) {
      paramList.value = Object.entries(modelInfo.model_params).map(([name, value]) => {
        const valObj: any = value
        if (typeof value === 'object') {
          return { name, value: String(valObj?.default) }
        } else {
          return { name, value: String(value) }
        }
      })
    }
    modelData.progress.value = 100
    saveNextParams({
      fileName: fileName,
      modelPro: modelData.progress.value,
      modelPath: path,
      modelResult: JSON.stringify(dataObj),
    })
  }
}
const endLoop = () => {
  if (intervalTimer) {
    clearInterval(intervalTimer)
  }
}
const modelName: Ref<string> = ref('')
const modelDesc = ref('')
const paramList: Ref<any> = ref([])
const getModelByPath = async (path: string) => {
  const info = await service.parseModel(path)
  if (Number(info.statusCode) === 200) {
    if (info.taskId) {
      startLoop(info.taskId)
    }
  }
}

const saveNextParams = (params: any) => {
  saveNodeParams(props.nodeData, {
    ...params,
  })
}
const resetUpload = () => {
  isShowUpload.value = true
  saveNextParams({
    fileName: '',
    modelPro: 0,
    modelPath: '',
    modelResult: '',
  })
  changeUpload(false)
  if (fileDropUpload.value) {
    fileDropUpload.value.resetUploading()
  }
}
onUnmounted(() => {
  // endLoop()
})
</script>
