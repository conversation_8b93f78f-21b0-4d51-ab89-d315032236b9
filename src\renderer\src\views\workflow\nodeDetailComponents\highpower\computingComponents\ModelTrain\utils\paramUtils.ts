// 参数映射和处理工具函数

export interface ModelParams {
  inChannels: string
  channels: string
  inputSegmentHeight: string
  inputSegmentWidth: string
  alpha: string
  kernelSize: string
  trainSupportSize: string
  testSupportSize: string
  actFn: {
    default: string
    choices: string[]
  }
  useFcForPrediction: boolean
  filterCyclesFlag: boolean
  featuresToDrop: string
  cyclesToDropInSegment: string
  returnPointwisePredictions: boolean
}

export interface ModelResult {
  modelName: string
  modelDesc: string
  modelParams: ModelParams
}

/**
 * 参数映射配置：后端字段名 -> 前端字段名
 */
export const PARAM_MAPPING = {
  // 数值参数
  in_channels: 'inChannels',
  channels: 'channels',
  input_segment_height: 'inputSegmentHeight',
  input_segment_width: 'inputSegmentWidth',
  alpha: 'alpha',
  kernel_size: 'kernelSize',
  train_support_size: 'trainSupportSize',
  test_support_size: 'testSupportSize',
  // 布尔值参数
  use_fc_for_prediction: 'useFcForPrediction',
  filter_cycles_flag: 'filterCyclesFlag',
  return_pointwise_predictions: 'returnPointwisePredictions',
  // 字符串参数
  features_to_drop: 'featuresToDrop',
  cycles_to_drop_in_segment: 'cyclesToDropInSegment',
} as const

/**
 * 创建默认的模型参数
 */
export const createDefaultModelParams = (): ModelParams => ({
  inChannels: '7',
  channels: '64',
  inputSegmentHeight: '20',
  inputSegmentWidth: '200',
  alpha: '0.5',
  kernelSize: '3',
  trainSupportSize: '50',
  testSupportSize: '200',
  actFn: {
    default: 'relu',
    choices: ['relu', 'tanh', 'sigmoid'],
  },
  useFcForPrediction: true,
  filterCyclesFlag: true,
  featuresToDrop: 'None',
  cyclesToDropInSegment: 'None',
  returnPointwisePredictions: false,
})

/**
 * 创建默认的模型结果
 */
export const createDefaultModelResult = (): ModelResult => ({
  modelName: '',
  modelDesc: '',
  modelParams: createDefaultModelParams(),
})

/**
 * 从配置中解析模型结果
 */
export const parseModelResultFromConfig = (config: any): ModelResult | null => {
  if (!config) return null

  // 尝试从 modelResult 中解析
  if (config.modelResult) {
    try {
      const modelData = JSON.parse(config.modelResult)
      return {
        modelName: modelData.name || '',
        modelDesc: modelData.description || '',
        modelParams: mapBackendParamsToFrontend(
          modelData.model_params || {},
          createDefaultModelParams(),
        ),
      }
    } catch (e) {
      console.error('解析 modelResult 失败:', e)
    }
  }

  // 兼容旧的格式
  if (config.model_params) {
    return {
      modelName: config.name || '',
      modelDesc: config.description || '',
      modelParams: mapBackendParamsToFrontend(config.model_params, createDefaultModelParams()),
    }
  }

  return null
}

/**
 * 从配置中解析模型参数
 */
export const parseModelParamsFromConfig = (config: any): any | null => {
  if (!config) return null

  // 尝试从 modelResult 中解析
  if (config.modelResult) {
    try {
      const modelData = JSON.parse(config.modelResult)
      return modelData.model_params
    } catch (e) {
      console.error('解析 modelResult 失败:', e)
    }
  }

  // 直接从 config 中获取
  if (config.model_params) {
    return config.model_params
  }

  return null
}

/**
 * 映射后端参数到前端参数
 */
export const mapBackendParamsToFrontend = (
  backendParams: any,
  targetParams: ModelParams,
): ModelParams => {
  const updatedParams = { ...targetParams }

  // 自动映射参数
  Object.entries(PARAM_MAPPING).forEach(([backendKey, frontendKey]) => {
    if (backendParams[backendKey] !== undefined) {
      ;(updatedParams as any)[frontendKey] = backendParams[backendKey]
    }
  })

  // 处理特殊的嵌套参数 act_fn
  if (backendParams.act_fn) {
    updatedParams.actFn.default = backendParams.act_fn.default || ''
    updatedParams.actFn.choices = backendParams.act_fn.choices || []
  }

  return updatedParams
}

/**
 * 更新模型结果从配置
 */
export const updateModelResultFromConfig = (
  config: any,
  currentResult: ModelResult,
  hasUserModified: boolean = false,
): ModelResult => {
  // 如果用户已经修改过参数，则不更新
  if (hasUserModified) {
    return currentResult
  }

  const parsedResult = parseModelResultFromConfig(config)

  if (parsedResult) {
    console.log('找到 modelResult，使用连接获取的默认值:', parsedResult)
    return parsedResult
  }

  return currentResult
}

/**
 * 更新模型参数从配置
 */
export const updateModelParamsFromConfig = (
  config: any,
  currentParams: ModelParams,
  hasUserModified: boolean = false,
): ModelParams => {
  // 如果用户已经修改过参数，则不更新
  if (hasUserModified) {
    return currentParams
  }

  const backendParams = parseModelParamsFromConfig(config)

  if (backendParams) {
    console.log('找到 modelParams，使用连接获取的默认值:', backendParams)
    return mapBackendParamsToFrontend(backendParams, currentParams)
  }

  return currentParams
}
