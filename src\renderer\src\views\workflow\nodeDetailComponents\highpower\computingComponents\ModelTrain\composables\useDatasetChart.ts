import { computed, nextTick, onMounted, onUnmounted, ref, watch, type Ref } from 'vue'
import * as echarts from 'echarts'
import { toast } from 'vue-sonner'

// 定义数据集项的接口
interface DatasetItem {
  id: string
  name: string
  type: 'train' | 'test' | 'val' | 'support' | 'all'
  selected: boolean
  isRecommended?: boolean
  startCycle?: number
  endCycle?: number
  rawData?: {
    file_path: string
    cycle_capacity_array: [number, number][]
    error: any
  }
}

export function useDatasetChart(
  dataset: Ref<DatasetItem | undefined>,
  isOpen: Ref<boolean>,
  emit: (
    event: 'update-chart-settings',
    itemId: string,
    startCycle: number,
    endCycle: number,
  ) => void,
) {
  // 响应式数据
  const chartContainer = ref<HTMLDivElement>()
  const rangeValues = ref<[number, number]>([0, 100])
  const startValue = ref(0)
  const endValue = ref(100)

  // 计算属性
  const datasetName = computed(() => dataset.value?.name || '未知数据集')

  const chartData = computed(() => {
    return dataset.value?.rawData?.cycle_capacity_array || []
  })

  const minCycle = computed(() => {
    if (chartData.value.length === 0) return 0
    return Math.min(...chartData.value.map((item) => item[0]))
  })

  const maxCycle = computed(() => {
    if (chartData.value.length === 0) return 100
    return Math.max(...chartData.value.map((item) => item[0]))
  })

  const filteredData = computed(() => {
    const [start, end] = rangeValues.value
    return chartData.value.filter((item) => item[0] >= start && item[0] <= end)
  })

  // 检查范围是否小于20圈
  const isRangeTooSmall = computed(() => {
    const [start, end] = rangeValues.value
    return end - start < 20
  })

  // ECharts 实例
  let chart: echarts.ECharts | null = null
  const resizeObserver = new ResizeObserver(() => {
    if (chart) {
      chart.resize()
    }
  })

  // 图表配置
  const getChartOption = () => ({
    title: {
      text: `${datasetName.value} - 循环容量曲线`,
      left: 'center',
      top: '8%',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
        color: '#333',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e5e7eb',
      borderWidth: 1,
      textStyle: {
        color: '#374151',
        fontSize: 12,
      },
      formatter: (params: any) => {
        const point = params[0]
        return `循环圈数: ${point.data[0]}<br>容量: ${point.data[1].toFixed(4)}`
      },
    },
    grid: {
      left: '7%',
      right: '8%',
      bottom: '10%',
      top: '18%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      name: '循环圈数',
      nameLocation: 'middle',
      nameGap: 30,
      nameTextStyle: {
        color: '#000',
        fontSize: 12,
        fontWeight: 'normal',
      },
      axisTick: {
        alignWithLabel: true,
      },
      axisLabel: {
        align: 'center',
        showMinLabel: true,
        showMaxLabel: true,
        color: '#000',
        fontSize: 11,
      },
      axisLine: {
        lineStyle: { color: '#e5e7eb' },
      },
      splitLine: {
        lineStyle: { color: '#f3f4f6', type: 'dashed' },
      },
    },
    yAxis: [
      // 左侧y轴 - 容量显示
      {
        type: 'value',
        name: '容量（Ah）',
        nameLocation: 'end',
        nameGap: 22,
        nameTextStyle: {
          padding: [0, 30, 0, 0],
          align: 'center',
          fontSize: 12,
          color: '#666',
        },
        axisLine: {
          lineStyle: { color: '#e5e7eb' },
        },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11,
          formatter: (value: number) => value.toFixed(0),
        },
        splitLine: {
          lineStyle: { color: '#f3f4f6', type: 'dashed' },
        },
        min: 0,
      },
      // 右侧y轴 - 容量(%)显示
      {
        type: 'value',
        name: '容量(%)',
        nameLocation: 'end',
        nameGap: 22,
        nameTextStyle: {
          padding: [0, 0, 0, 50],
          align: 'right',
          fontSize: 12,
          color: '#666',
        },
        position: 'right',
        axisLine: {
          lineStyle: { color: '#e5e7eb' },
          show: true, // 确保显示轴线
        },
        axisLabel: {
          color: '#6b7280',
          fontSize: 11,
          formatter: (value: number) => `${value.toFixed(0)}%`,
          show: true, // 确保显示刻度标签
        },
        axisTick: {
          show: true, // 确保显示刻度线
        },
        splitLine: {
          show: false, // 右侧y轴不显示分割线
        },
        min: 0,
        max: 100, // 设置最大值为100%
        // 与左侧y轴使用相同的刻度范围
        alignTicks: true,
      },
    ],
    visualMap: {
      type: 'piecewise',
      dimension: 0,
      pieces: [
        {
          gte: rangeValues.value[0],
          lte: rangeValues.value[1],
          color: '#ef4444', // 选中范围内的颜色 - 红色
        },
        {
          lt: rangeValues.value[0],
          color: '#3b82f6', // 选中范围外的颜色 - 蓝色
        },
        {
          gt: rangeValues.value[1],
          color: '#3b82f6', // 选中范围外的颜色 - 蓝色
        },
      ],
      show: false,
    },
    series: [
      {
        name: '循环容量',
        type: 'line',
        data: chartData.value,
        smooth: true,
        symbol: 'none',
        sampling: 'lttb',
        lineStyle: {
          width: 2,
        },
        markLine: {
          silent: true, // 不响应鼠标事件
          symbol: 'none', // 不显示线条两端的箭头符号
          // 全局的 lineStyle 如果不需要，可以移除或设置为一个默认色
          // lineStyle: {
          //   width: 1,
          //   type: 'dashed',
          //   color: '#ccc', // 默认线条颜色，会被data中的lineStyle覆盖
          // },
          data: [
            {
              // ====== 起始线 ======
              xAxis: rangeValues.value[0],
              lineStyle: {
                width: 1,
                type: 'dashed',
                color: '#22c55e',
              },
              label: {
                formatter: `起始: ${rangeValues.value[0]}`,
                position: 'end',
                color: '#22c55e',
                // fontSize: 12,
                // fontWeight: 'bold'
              },
            },
            {
              // ====== 终止线 ======
              xAxis: rangeValues.value[1],
              lineStyle: {
                width: 1,
                type: 'dashed',
                color: '#ef4444',
              },
              label: {
                formatter: `终止: ${rangeValues.value[1]}`,
                position: 'end',
                color: '#ef4444',
                // fontSize: 12,
                // fontWeight: 'bold'
              },
            },
          ],
        },
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomLock: false,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false,
        preventDefaultMouseMove: true,
      },
    ],
    animation: true,
    animationDuration: 300,
  })

  // 方法
  const initChart = async () => {
    if (!chartContainer.value) return

    await nextTick()

    if (chart) {
      chart.dispose()
    }

    chart = echarts.init(chartContainer.value)
    chart.setOption(getChartOption())

    resizeObserver.observe(chartContainer.value)
  }

  const updateChart = () => {
    if (chart) {
      chart.setOption(getChartOption())
    }
  }

  const resetRange = () => {
    // 优先使用数据集项中存储的 startCycle 和 endCycle
    const defaultStart = dataset.value?.startCycle || minCycle.value
    const defaultEnd = dataset.value?.endCycle || maxCycle.value

    rangeValues.value = [defaultStart, defaultEnd]
    startValue.value = defaultStart
    endValue.value = defaultEnd
  }

  const updateRangeFromInput = () => {
    rangeValues.value = [startValue.value, endValue.value]
  }

  const confirmSelection = () => {
    const [startCycle, endCycle] = rangeValues.value

    // 检查范围是否有效
    if (isRangeTooSmall.value) {
      toast.error('圈数范围过小，请重新选择！至少20圈！')
      return
    }

    // 发送图表设置更新事件
    if (dataset.value?.id) {
      emit('update-chart-settings', dataset.value.id, startCycle, endCycle)
      console.log('确认选择范围:', { itemId: dataset.value.id, startCycle, endCycle })

      // 获取文件名
      const filePath = dataset.value?.rawData?.file_path || ''
      const fileName = filePath ? filePath.split(/[/\\]/).pop() || '未知文件' : '未知文件'

      toast.success(fileName, {
        description: `已设置图表范围：${startCycle} - ${endCycle}`,
      })
    }

    // 关闭对话框
    isOpen.value = false
  }

  const exportData = () => {
    if (filteredData.value.length === 0) {
      toast.error('没有数据可导出')
      return
    }

    const csvContent = [
      'Cycle,Capacity',
      ...filteredData.value.map((item) => `${item[0]},${item[1]}`),
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${datasetName.value}_data.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success('数据导出成功')
  }

  // 监听器
  watch(isOpen, async (newValue) => {
    if (newValue && dataset.value) {
      await nextTick()
      resetRange()
      await initChart()
    }
  })

  // 监听 dataset 变化，重置范围
  watch(
    () => dataset.value,
    (newDataset) => {
      if (newDataset && isOpen.value) {
        resetRange()
        updateChart()
      }
    },
    { deep: true },
  )

  watch(
    rangeValues,
    (newValues) => {
      startValue.value = newValues[0]
      endValue.value = newValues[1]
      updateChart()
    },
    { deep: true },
  )

  // 生命周期
  onMounted(() => {
    if (isOpen.value && dataset.value) {
      initChart()
    }
  })

  onUnmounted(() => {
    if (chart) {
      chart.dispose()
    }
    resizeObserver.disconnect()
  })

  return {
    // 状态
    chartContainer,
    rangeValues,
    startValue,
    endValue,

    // 计算属性
    datasetName,
    chartData,
    minCycle,
    maxCycle,
    filteredData,
    isRangeTooSmall,

    // 方法
    initChart,
    updateChart,
    resetRange,
    updateRangeFromInput,
    confirmSelection,
    exportData,
  }
}
